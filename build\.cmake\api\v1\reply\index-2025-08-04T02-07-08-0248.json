{"cmake": {"generator": {"multiConfig": false, "name": "MinGW Makefiles"}, "paths": {"cmake": "D:/Cmake3.28/bin/cmake.exe", "cpack": "D:/Cmake3.28/bin/cpack.exe", "ctest": "D:/Cmake3.28/bin/ctest.exe", "root": "D:/Cmake3.28/share/cmake-3.28"}, "version": {"isDirty": false, "major": 3, "minor": 28, "patch": 2, "string": "3.28.2", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-f19e7e74b25dc7904127.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, {"jsonFile": "cache-v2-b1ce7a1742219169080b.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-0ed4503e3901a0407455.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-dad44b54adb05f503830.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-b1ce7a1742219169080b.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-f19e7e74b25dc7904127.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, {"jsonFile": "toolchains-v1-dad44b54adb05f503830.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-0ed4503e3901a0407455.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}]}}}}